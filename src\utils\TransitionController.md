# TransitionController Documentation

## Overview

TransitionController adalah sistem manajemen transisi yang fleksibel yang mendukung penggunaan **View Transition API** dan **CSS transitions** secara bersamaan atau terpisah. Ini memberikan developer kont<PERSON> penuh atas bagaimana transisi antar halaman bekerja.

## Fitur Utama

### 1. **Dual Transition Support**
- Gunakan View Transition API + CSS transitions secara bersamaan
- Contoh: View Transition API untuk microphone morphing + CSS untuk text animations

### 2. **Single Transition Support**
- Gunakan hanya View Transition API
- Gunakan hanya CSS transitions
- Pilihan fleksibel berdasarkan kebutuhan

### 3. **Mix and Match**
- Elemen berbeda bisa menggunakan jenis transisi berbeda dalam satu navigasi
- Modular dan dapat dikonfigurasi

### 4. **Dynamic Configuration**
- Update pengaturan transisi saat runtime
- Tidak perlu restart aplikasi

## Struktur Konfigurasi

```javascript
{
  viewTransition: {
    enabled: boolean,           // Aktifkan View Transition API
    elements: string[]          // Optional: elemen spesifik untuk View Transition API
  },
  cssTransition: {
    enabled: boolean,           // Aktifkan CSS transitions
    cssClass: string,           // CSS class yang diterapkan ke body
    elements: string[]          // Optional: elemen spesifik untuk CSS transitions
  },
  duration: number              // Durasi total transisi (ms)
}
```

## Contoh Penggunaan

### 1. Dual Transitions (Rekomendasi untuk UX terbaik)

```javascript
// Welcome → Test: View Transition API untuk microphone + CSS untuk text
TransitionController.enableDualTransitions(
  'welcome-to-test',
  'welcome-to-test-transition',
  ['microphone-button'],        // View Transition API elements
  ['welcome-text', 'test-text'] // CSS transition elements
);
```

### 2. View Transition API Only

```javascript
// Transisi otomatis dan smooth tanpa CSS tambahan
TransitionController.enableViewTransitionOnly('test-to-result');
```

### 3. CSS Transitions Only

```javascript
// Kontrol penuh dengan CSS, fallback untuk browser lama
TransitionController.enableCssTransitionOnly(
  'result-to-welcome',
  'result-to-welcome-transition'
);
```

### 4. Update Konfigurasi Manual

```javascript
// Update konfigurasi secara detail
TransitionController.updateTransitionConfig('test-to-result', {
  viewTransition: { 
    enabled: true,
    elements: [] 
  },
  cssTransition: { 
    enabled: true,
    cssClass: 'test-to-result-transition',
    elements: ['floating-microphone']
  },
  duration: 800
});
```

## Konfigurasi Saat Ini

### Welcome → Test (DUAL TRANSITIONS)
- **View Transition API**: ✅ (microphone morphing)
- **CSS Transitions**: ✅ (text animations)
- **Hasil**: Microphone smooth morphing + text fade dengan arah custom
- **Pola**: Dual - kombinasi terbaik untuk UX optimal

### Test → Welcome (DUAL TRANSITIONS)
- **View Transition API**: ✅ (microphone morphing)
- **CSS Transitions**: ✅ (text animations)
- **Hasil**: Microphone smooth morphing + text fade tanpa sliding
- **Pola**: Dual - konsisten dengan welcome → test

### Test → Result (CSS ONLY)
- **View Transition API**: ❌ (disabled)
- **CSS Transitions**: ✅ (microphone disappear + page transition)
- **Hasil**: Microphone menghilang dengan CSS + text fade in place
- **Pola**: CSS only - kontrol penuh atas animasi microphone

### Result → Welcome (CSS ONLY)
- **View Transition API**: ❌ (disabled)
- **CSS Transitions**: ✅ (text animations)
- **Hasil**: Text fade dari bawah dengan CSS
- **Pola**: CSS only - konsisten dengan test → result

## Helper Methods

```javascript
// Cek status transisi
TransitionController.isViewTransitionEnabled('welcome-to-test');
TransitionController.isCssTransitionEnabled('test-to-result');
TransitionController.isDualTransitionEnabled('test-to-welcome');

// Dapatkan konfigurasi
const config = TransitionController.getTransitionConfig('welcome-to-test');
```

## Keuntungan Arsitektur Baru

1. **Fleksibilitas**: Developer bisa memilih jenis transisi sesuai kebutuhan
2. **Performa**: Hanya aktifkan transisi yang diperlukan
3. **Kompatibilitas**: Fallback otomatis untuk browser yang tidak support View Transition API
4. **Maintainability**: Konfigurasi terpusat dan mudah diubah
5. **Debugging**: Log yang jelas untuk setiap jenis transisi

## Best Practices

1. **Gunakan dual transitions** untuk UX terbaik (View Transition API + CSS)
2. **Gunakan View Transition API only** untuk transisi sederhana
3. **Gunakan CSS only** untuk kontrol detail atau fallback
4. **Test di berbagai browser** untuk memastikan kompatibilitas
5. **Monitor performa** terutama pada device low-end

## Migration dari Versi Lama

Konfigurasi lama masih kompatibel, tapi disarankan untuk update ke format baru:

```javascript
// Lama
{
  useViewTransition: true,
  microphoneTransition: true,
  cssClass: 'welcome-to-test-transition'
}

// Baru (lebih fleksibel)
{
  viewTransition: { enabled: true, elements: ['microphone-button'] },
  cssTransition: { enabled: true, cssClass: 'welcome-to-test-transition' }
}
```
