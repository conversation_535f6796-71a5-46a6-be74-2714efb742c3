import TransitionController from '../utils/TransitionController.js';
import RecordingManager from '../utils/RecordingManager.js';
import MicrophoneIcon from '../assets/MicrophoneIcon.js';

class WelcomeView {
  constructor() {
    this.container = null;
  }

  render() {
    this.container = document.createElement('div');
    this.container.className = 'container';

    const welcomeText = document.createElement('h1');
    welcomeText.textContent = 'Wanna test how good your speaking skill is?';
    welcomeText.className = 'welcome-text';

    const micButton = document.createElement('button');
    MicrophoneIcon.setInnerHTML(micButton, { className: 'microphone-icon' });
    micButton.className = 'microphone-button';

    micButton.addEventListener('click', () => {
      console.log('🎤 Microphone button clicked - starting navigation');

      // Start navigation first to trigger transition
      window.location.hash = '#/test';

      // Start recording after a small delay to let transition begin
      setTimeout(() => {
        RecordingManager.startRecordingFromWelcome().catch(error => {
          console.error('Failed to start recording:', error);
        });
      }, 100);
    });

    // Debug button for testing transition without recording
    const debugButton = document.createElement('button');
    debugButton.textContent = 'Test Transition (Debug)';
    debugButton.style.cssText = 'margin: 20px; padding: 10px; background: orange; color: white; border: none; border-radius: 5px;';
    debugButton.addEventListener('click', () => {
      console.log('🐛 Debug button clicked - testing transition only');
      window.location.hash = '#/test';
    });

    this.container.appendChild(welcomeText);
    this.container.appendChild(micButton);
    this.container.appendChild(debugButton);

    const currentTransition = TransitionController.getCurrentTransition();
    const isFromTestPage = currentTransition === 'test-to-welcome';

    if (isFromTestPage) {
      welcomeText.classList.add('visible');
      micButton.classList.add('visible');
    } else {
      setTimeout(() => {
        welcomeText.classList.add('visible');
        micButton.classList.add('visible');
      }, 100);
    }

    return this.container;
  }

  destroy() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}

export default WelcomeView;
