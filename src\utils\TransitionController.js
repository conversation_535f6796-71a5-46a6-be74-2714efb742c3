/**
 * TransitionController - Flexible transition management supporting both View Transition API and CSS transitions
 *
 * Features:
 * - Dual transition support: Use View Transition API + CSS transitions simultaneously
 * - Single transition support: Use only View Transition API or only CSS transitions
 * - Mix and match: Different elements can use different transition types in the same navigation
 * - Dynamic configuration: Update transition settings at runtime
 *
 * Configuration Structure:
 * {
 *   viewTransition: {
 *     enabled: boolean,
 *     elements: string[] // Optional: specific elements for View Transition API
 *   },
 *   cssTransition: {
 *     enabled: boolean,
 *     cssClass: string, // CSS class applied to body during transition
 *     elements: string[] // Optional: specific elements for CSS transitions
 *   },
 *   duration: number // Total transition duration in milliseconds
 * }
 *
 * Examples:
 *
 * 1. Dual transitions (View Transition API + CSS):
 *    - View Transition API handles microphone morphing
 *    - CSS transitions handle text animations with specific directions
 *
 * 2. View Transition API only:
 *    - Automatic page transitions with smooth morphing
 *    - No additional CSS classes needed
 *
 * 3. CSS transitions only:
 *    - Custom CSS animations with full control
 *    - Fallback for browsers without View Transition API support
 */
class TransitionController {
  constructor() {
    this.currentTransition = null;
    this.transitionTypes = {
      INITIAL_LOAD: 'initial-load',
      WELCOME_TO_TEST: 'welcome-to-test',
      TEST_TO_WELCOME: 'test-to-welcome',
      TEST_TO_RESULT: 'test-to-result',
      RESULT_TO_WELCOME: 'result-to-welcome',
      DIRECT: 'direct'
    };
    
    this.transitionConfigs = {
      [this.transitionTypes.INITIAL_LOAD]: {
        viewTransition: {
          enabled: false
        },
        cssTransition: {
          enabled: false
        },
        duration: 0
      },

      [this.transitionTypes.WELCOME_TO_TEST]: {
        viewTransition: {
          enabled: true,
          elements: ['microphone-button'] // View Transition API handles microphone morphing
        },
        cssTransition: {
          enabled: true,
          cssClass: 'welcome-to-test-transition'
        },
        duration: 600
      },

      [this.transitionTypes.TEST_TO_WELCOME]: {
        viewTransition: {
          enabled: true,
          elements: ['microphone-button'] // View Transition API handles microphone morphing
        },
        cssTransition: {
          enabled: true,
          cssClass: 'test-to-welcome-transition'
        },
        duration: 600
      },

      [this.transitionTypes.TEST_TO_RESULT]: {
        viewTransition: {
          enabled: false // Only CSS transitions
        },
        cssTransition: {
          enabled: true,
          cssClass: 'test-to-result-transition',
          elements: ['floating-microphone'] // CSS handles microphone disappearing
        },
        duration: 600
      },

      [this.transitionTypes.RESULT_TO_WELCOME]: {
        viewTransition: {
          enabled: false // Only CSS transitions
        },
        cssTransition: {
          enabled: true,
          cssClass: 'result-to-welcome-transition'
        },
        duration: 600
      },

      [this.transitionTypes.DIRECT]: {
        viewTransition: {
          enabled: false
        },
        cssTransition: {
          enabled: false
        },
        duration: 0
      }
    };
  }

  async navigate(targetRoute, transitionType, navigationCallback) {
    const config = this.transitionConfigs[transitionType];

    if (!config) {
      console.warn(`Unknown transition type: ${transitionType}. Using direct navigation.`);
      return navigationCallback();
    }

    // Debug logging
    console.log(`🎬 Transition: ${transitionType} → ${targetRoute}`, config);

    // Set current transition for CSS targeting
    this.currentTransition = transitionType;

    // Add CSS transition class to body for CSS targeting
    if (config.cssTransition.enabled && config.cssTransition.cssClass) {
      document.body.classList.add(config.cssTransition.cssClass);

      // Mark elements that are about to leave for proper targeting
      this.markLeavingElements();
    }

    try {
      // Check if both transitions are enabled
      const useViewTransition = config.viewTransition.enabled && document.startViewTransition;
      const useCssTransition = config.cssTransition.enabled;

      if (useViewTransition && useCssTransition) {
        // Use both View Transition API and CSS transitions simultaneously
        console.log(`🎭 Using dual transitions: View Transition API + CSS`);

        // For dual transitions, we need to coordinate both
        // View Transition API will handle specific elements (like microphone)
        // CSS transitions will handle other elements (like text)
        await document.startViewTransition(() => {
          navigationCallback();
        }).finished;
      } else if (useViewTransition) {
        // Use only View Transition API
        console.log(`🎬 Using View Transition API only`);
        await document.startViewTransition(() => {
          navigationCallback();
        }).finished;
      } else if (useCssTransition) {
        // Use only CSS transitions
        console.log(`🎨 Using CSS transitions only`);
        navigationCallback();
        // Wait for CSS transition duration
        await new Promise(resolve => setTimeout(resolve, config.duration));
      } else {
        // Direct navigation without transition
        console.log(`⚡ Direct navigation (no transitions)`);
        navigationCallback();
      }
    } catch (error) {
      console.error('Transition failed:', error);
      // Fallback to direct navigation
      navigationCallback();
    } finally {
      // Clean up CSS transition class
      if (config.cssTransition.enabled && config.cssTransition.cssClass) {
        setTimeout(() => {
          document.body.classList.remove(config.cssTransition.cssClass);
        }, config.duration);
      }

      this.currentTransition = null;
    }
  }

  getTransitionType(fromRoute, toRoute, isInitialLoad = false) {
    if (isInitialLoad) {
      return this.transitionTypes.INITIAL_LOAD;
    }

    const transitionMap = {
      '/ -> /test': this.transitionTypes.WELCOME_TO_TEST,
      '/test -> /': this.transitionTypes.TEST_TO_WELCOME,
      '/test -> /result': this.transitionTypes.TEST_TO_RESULT,
      '/result -> /': this.transitionTypes.RESULT_TO_WELCOME
    };

    const transitionKey = `${fromRoute} -> ${toRoute}`;
    return transitionMap[transitionKey] || this.transitionTypes.DIRECT;
  }

  navigateWelcomeToTest(navigationCallback) {
    return this.navigate('/test', this.transitionTypes.WELCOME_TO_TEST, navigationCallback);
  }

  navigateTestToWelcome(navigationCallback) {
    return this.navigate('/', this.transitionTypes.TEST_TO_WELCOME, navigationCallback);
  }

  getCurrentTransition() {
    return this.currentTransition;
  }

  isTransitionActive(transitionType) {
    return this.currentTransition === transitionType;
  }

  // Helper methods for checking transition capabilities
  isViewTransitionEnabled(transitionType) {
    const config = this.transitionConfigs[transitionType];
    return config && config.viewTransition.enabled;
  }

  isCssTransitionEnabled(transitionType) {
    const config = this.transitionConfigs[transitionType];
    return config && config.cssTransition.enabled;
  }

  isDualTransitionEnabled(transitionType) {
    return this.isViewTransitionEnabled(transitionType) && this.isCssTransitionEnabled(transitionType);
  }

  getTransitionConfig(transitionType) {
    return this.transitionConfigs[transitionType] || null;
  }

  // Method to update transition configuration dynamically
  updateTransitionConfig(transitionType, updates) {
    if (this.transitionConfigs[transitionType]) {
      this.transitionConfigs[transitionType] = {
        ...this.transitionConfigs[transitionType],
        ...updates
      };
      console.log(`🔧 Updated transition config for ${transitionType}:`, this.transitionConfigs[transitionType]);
    } else {
      console.warn(`Cannot update config for unknown transition type: ${transitionType}`);
    }
  }

  // Example usage methods for developers

  /**
   * Enable only View Transition API for a specific transition
   * @param {string} transitionType - The transition type to modify
   */
  enableViewTransitionOnly(transitionType) {
    this.updateTransitionConfig(transitionType, {
      viewTransition: { enabled: true },
      cssTransition: { enabled: false }
    });
  }

  /**
   * Enable only CSS transitions for a specific transition
   * @param {string} transitionType - The transition type to modify
   * @param {string} cssClass - CSS class to apply during transition
   */
  enableCssTransitionOnly(transitionType, cssClass) {
    this.updateTransitionConfig(transitionType, {
      viewTransition: { enabled: false },
      cssTransition: { enabled: true, cssClass }
    });
  }

  /**
   * Enable dual transitions (both View Transition API and CSS)
   * @param {string} transitionType - The transition type to modify
   * @param {string} cssClass - CSS class to apply during transition
   * @param {string[]} viewElements - Elements for View Transition API
   * @param {string[]} cssElements - Elements for CSS transitions
   */
  enableDualTransitions(transitionType, cssClass, viewElements = [], cssElements = []) {
    this.updateTransitionConfig(transitionType, {
      viewTransition: { enabled: true, elements: viewElements },
      cssTransition: { enabled: true, cssClass, elements: cssElements }
    });
  }

  // Preset configurations for common use cases

  /**
   * Apply preset: Microphone morphing with text animations
   * Best for: welcome ↔ test transitions
   */
  applyMicrophoneMorphingPreset(transitionType, cssClass) {
    this.enableDualTransitions(
      transitionType,
      cssClass,
      ['microphone-button'],  // View Transition API for microphone
      ['welcome-text', 'test-text']  // CSS for text animations
    );
  }

  /**
   * Apply preset: Page transition with element disappearing
   * Best for: test → result transitions
   */
  applyElementDisappearPreset(transitionType, cssClass, disappearingElements) {
    this.enableDualTransitions(
      transitionType,
      cssClass,
      [],  // View Transition API for page
      disappearingElements  // CSS for specific elements
    );
  }

  /**
   * Apply preset: Simple page transition
   * Best for: result → welcome transitions
   */
  applySimplePageTransitionPreset(transitionType, cssClass) {
    this.enableDualTransitions(
      transitionType,
      cssClass,
      [],  // View Transition API for page
      []   // CSS for additional effects
    );
  }

  // Helper method to mark elements that are leaving
  markLeavingElements() {
    const currentElements = document.querySelectorAll('[data-page]');

    // Mark current page elements as leaving
    currentElements.forEach(element => {
      element.setAttribute('data-transition-state', 'leaving');
    });

    // Remove the marking after a short delay to avoid affecting new elements
    setTimeout(() => {
      currentElements.forEach(element => {
        element.removeAttribute('data-transition-state');
      });
    }, 50);
  }

  async initializeMicrophone(testPresenter) {
    if (testPresenter && typeof testPresenter.initializeAudio === 'function') {
      try {
        await testPresenter.initializeAudio();
        console.log('🎤 Microphone initialized via TransitionController');
        return true;
      } catch (error) {
        console.error('❌ Failed to initialize microphone via TransitionController:', error);
        return false;
      }
    }
    return false;
  }

  async startRecording(testPresenter) {
    if (testPresenter && typeof testPresenter.initializeAndStartRecording === 'function') {
      try {
        await testPresenter.initializeAndStartRecording();
        return true;
      } catch (error) {
        console.error('❌ Failed to start recording via TransitionController:', error);
        return false;
      }
    }
    return false;
  }
}

export default new TransitionController();
